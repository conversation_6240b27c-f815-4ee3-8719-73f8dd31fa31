<?php

use Bo<PERSON>ble\Base\Forms\FieldOptions\HtmlFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\Fields\HtmlField;
use Bo<PERSON>ble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Bo<PERSON>ble\Shortcode\Compilers\Shortcode as ShortcodeCompiler;
use Bo<PERSON>ble\Shortcode\Facades\Shortcode;
use Bo<PERSON>ble\Shortcode\Forms\ShortcodeForm;
use Botble\Theme\Facades\Theme;
use Carbon\Carbon;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Plugin\Lottery\Enums\LotteryRegionEnum;
use Plugin\Lottery\Enums\LotteryResultLevelEnum;
use Plugin\Lottery\Facades\LotteryStatistic;
use Plugin\Lottery\Facades\LotterySupport;
use Plugin\Lottery\Models\LotteryCityDate;

app('events')->listen(RouteMatched::class, function (): void {
    if (is_plugin_active('lottery')) {
        function setMetaHeader()
        {
            $schedules = LotterySupport::schedules();
            $time = Carbon::now()->format('H:i');
            $schedule = collect($schedules)
                ->where('enabled', true)
                ->where('start_time')
                ->map(function ($item, $key) {
                    return array_merge($item, ['key' => $key]);
                })
                ->sortBy('start_time')
                ->filter(function ($item) use ($time) {
                    return $time < Arr::get($item, 'start_time');
                })
                ->first();

            if ($schedule) {
                Theme::set('headerMeta', '<meta http-equiv="refresh" data-start-time="'. Arr::get($schedule, 'start_time') .'" key="'. $schedule['key'] .'" content="'. round(Carbon::now()->diffInSeconds(Carbon::parse(Arr::get($schedule, 'start_time')), true)) .'">');
            }
        }

        Shortcode::register(
            'lottery-latest-numbers',
            __('Lottery latest numbers'),
            __('Lottery latest numbers'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;

                $view = LotterySupport::getPartialTableView($region);

                if (! $view) {
                    return null;
                }

                if ($shortcode->show_live != 'no') {
                    if (LotterySupport::isRegionLiveRunning($region)) {
                        return Theme::partial('lottery.shortcodes.region-live', compact('region'));
                    }
                }

                if ($shortcode->by == 'yesterday') {
                    $cityDate = (object) [
                        'city_id' => null,
                        'date' => Carbon::yesterday()->toDateString(),
                    ];
                } else {
                    setMetaHeader();

                    $cityDate = LotterySupport::getLatestCityDateByRegion($region);
                }

                if (! $cityDate) {
                    return null;
                }

                $date = Carbon::create($cityDate->date);

                $cities = LotterySupport::getCitiesByRegion($region, $date);

                if ($shortcode->city_id) {
                    $cities = $cities->where('id', $shortcode->city_id);
                }

                if ($region == 'dt') {
                    // Lấy ID của record mới nhất cho mỗi city
                    $latestIds = LotteryCityDate::query()
                        ->selectRaw('MAX(id) as max_id')
                        ->whereIn('city_id', $cities->pluck('id')->all())
                        ->groupBy('city_id')
                        ->pluck('max_id');

                    $cityDates = LotteryCityDate::query()
                        ->whereIn('id', $latestIds)
                        ->latest('date')
                        ->getQuery()
                        ->get();

                    // dientoan6x36 -> dientoan123 -> thantai4
                    $cities = $cities
                        ->sortBy(function ($city) {
                            $order = [
                                'dientoan6x36' => 1,
                                'dientoan123' => 2,
                                'thantai4' => 3,
                            ];

                            return Arr::get($order, Str::lower($city->code), 99);
                        })
                        ->values();
                } else {
                    $cityDates = LotteryCityDate::query()
                        ->whereIn('city_id', $cities->pluck('id')->all())
                        ->where('date', $cityDate->date)
                        ->latest('date')
                        ->getQuery()
                        ->get();
                }

                $numbers = LotterySupport::getNumbersByCityDates($region, $cityDates);

                // $numbers = LotterySupport::getNumbersByDate($region, $date);

                return Theme::partial($view, compact('numbers', 'cities', 'date', 'region', 'cityDates', 'shortcode'));
            }
        )
        ->setAdminConfig('lottery-latest-numbers', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'heading_type',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Heading Type'))
                        ->choices([
                            'h1' => 'H1',
                            'h2' => 'H2',
                            'h3' => 'H3',
                            'h4' => 'H4',
                            'h5' => 'H5',
                            'h6' => 'H6',
                        ])
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(LotteryRegionEnum::labels())
                )
                ->add(
                    'city_id',
                    HtmlField::class,
                    HtmlFieldOption::make()
                        ->content(Theme::partial('lottery.cities-by-region', compact('attributes')))
                )
                ->add(
                    'by',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('By'))
                        ->choices([
                            '' => 'Default',
                            'yesterday' => 'Yesterday',
                        ])
                )
                ->add(
                    'show_live',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Show live'))
                        ->choices([
                            '' => 'Yes',
                            'no' => 'No',
                        ])
                )
                ->add(
                    'class_name',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Class Name'))
                );
        });

        Shortcode::register(
            'lottery-zero-prize-statistics',
            __('Lottery zero prize statistics'),
            __('Lottery zero prize statistics'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;

                $view = match ($region) {
                    LotteryRegionEnum::MB => 'lottery.zero-prize-statistics',
                    LotteryRegionEnum::MN, LotteryRegionEnum::MT => 'lottery.zero-prize-statistics',
                    default => null,
                };

                if (! $view) {
                    return null;
                }

                $date = Carbon::today();
                $model = LotteryRegionEnum::getModelQuery($region);
                $period = $shortcode->period ?: 30;

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('WEEKOFYEAR(date) AS week_of_year, DAYOFWEEK(date) - 1 AS day_of_week'),
                        DB::raw('YEARWEEK(date, 3) AS iso_year_week'),
                    ])
                    ->where('date', '<=', $date)
                    ->where('date', '>=', $date->clone()->subDays($period))
                    ->where('level', LotteryResultLevelEnum::ZERO)
                    ->getQuery()
                    ->get();

                $range = $period;
                return Theme::partial($view, compact('numbers', 'date', 'region', 'range', 'shortcode'));
            }
        )
        ->setAdminConfig('lottery-zero-prize-statistics', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(LotteryRegionEnum::labels())
                )
                ->add(
                    'period',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Period'))
                        ->choices([
                            '' => 'None',
                            '7' => '7 ngày',
                            '10' => '10 ngày',
                            '15' => '15 ngày',
                            '30' => '30 ngày',
                        ])
                );
        });

        Shortcode::register(
            'lottery-region-live',
            __('Lottery region live'),
            __('Lottery region live'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;

                Theme::set('region-submenu', $region);

                setMetaHeader();

                return Theme::partial('lottery.shortcodes.region-live', compact('region'));
            }
        )
        ->setAdminConfig('lottery-region-live', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->withLazyLoading()
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(LotteryRegionEnum::labels())
                );
        });

        Shortcode::register(
            'lottery-live-notification',
            __('Lottery live notification'),
            __('Lottery live notification'),
            function (ShortcodeCompiler $shortcode) {
                $regions = LotteryRegionEnum::labels();
                $schedules = LotterySupport::schedules();

                $currentDate = Carbon::today();

                return Theme::partial('lottery.shortcodes.live-notification', compact('shortcode', 'regions', 'schedules'));
            }
        )
        ->setAdminConfig('lottery-live-notification', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->tap(function ($form) {
                    foreach (LotteryRegionEnum::labels() as $key => $label) {
                        $form
                            ->add(
                                'url_' . $key,
                                TextField::class,
                                TextFieldOption::make()
                                    ->label(__('Url Region ' . $label))
                            );
                    }
                });
        });

        Shortcode::register(
            'lottery-thong-ke-dau-duoi',
            __('Lottery Thống kê đầu/đuôi'),
            __('Lottery Thống kê đầu/đuôi'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $endDate = $startDate->copy()->subDays($range);

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $type = $shortcode->type ?: 'dau';

                if ($type == 'duoi') {
                    $select = DB::raw('RIGHT(number, 1) AS one_digit');
                } else {
                    $select = DB::raw('SUBSTRING(number FROM LENGTH(number) - 1 FOR 1) AS one_digit');
                }

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        $select,
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-dau-duoi', compact('city', 'numbers', 'type'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-dau-duoi', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'type',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Type'))
                        ->choices([
                            'dau' => 'Thống kê đầu',
                            'duoi' => 'Thống kê đuôi',
                        ])
                );
        });

        Shortcode::register(
            'lottery-thong-ke-loto-gan',
            __('Lottery Thống kê loto-gan'),
            __('Lottery Thống kê loto-gan'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;

                if (! $region) {
                    $city = LotteryStatistic::getRequestCity();
                    $region = $city->region;
                    $cityIds = [$city->id];
                } else {
                    $cities = LotterySupport::getCitiesByRegion($region);
                    $cityIds = $cities->pluck('id')->all();

                    $city = $cities->first();
                }

                $yesterday = Carbon::yesterday();
                $date = LotteryStatistic::getRequestDate('d-m-Y');

                $startDate = $date;
                $model = LotteryRegionEnum::getModelQuery($region);

                $period = $shortcode->period ?: 60;
                $cityDates = LotteryCityDate::query()
                    ->whereIn('city_id', $cityIds)
                    ->where('date', '<=', $date)
                    ->limit($period)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = Carbon::parse($cityDates->last()->date);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->whereIn('city_id', $cityIds)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-loto-gan', compact('city', 'numbers', 'startDate', 'endDate', 'cityDates', 'shortcode'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-loto-gan', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(['' => 'None'] + Arr::except(LotteryRegionEnum::labels(), LotteryRegionEnum::DT))
                )
                ->add(
                    'period',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Period'))
                        ->choices([
                            '' => 'Default',
                            '7' => '7 ngày',
                            '15' => '15 ngày',
                            '30' => '30 ngày',
                            '60' => '60 ngày',
                        ])
                )
                ->add(
                    'display',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Display'))
                        ->choices([
                            '' => 'Default',
                            'shorten' => 'Rút gọn',
                        ])
                );
        });

        Shortcode::register(
            'lottery-thong-ke-gan-theo-loto',
            __('Lottery Thống kê gan-theo-loto'),
            __('Lottery Thống kê gan-theo-loto'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();

                $startDate = Carbon::today();

                $range = 60;

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit(60)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-gan-theo-loto', compact('city', 'numbers', 'cityDates', 'startDate'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-gan-theo-loto', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-loto-xien',
            __('Lottery Thống kê loto xien'),
            __('Lottery Thống kê loto xien'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $endDate = $startDate->copy()->subDays($range);

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-loto-xien', compact('city', 'numbers', 'cityDates', 'range'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-loto-xien', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-00-99',
            __('Lottery Thống kê 00 - 99'),
            __('Lottery Thống kê 00 - 99'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit($range)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-00-99', compact('city', 'numbers', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-00-99', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-tan-suat',
            __('Lottery Thống kê tần suất'),
            __('Lottery Thống kê tần suất'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;

                if (! $region) {
                    $city = LotteryStatistic::getRequestCity();
                    $region = $city->region;
                    $cityIds = [$city->id];
                } else {
                    $cities = LotterySupport::getCitiesByRegion($region);
                    $cityIds = $cities->pluck('id')->all();

                    $city = $cities->first();
                }

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $cityDates = LotteryCityDate::query()
                    ->whereIn('city_id', $cityIds)
                    ->where('date', '<=', $startDate)
                    ->limit($range)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->whereIn('city_id', $cityIds)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-tan-suat', compact('shortcode', 'city', 'numbers', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-tan-suat', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(['' => 'None'] + Arr::except(LotteryRegionEnum::labels(), LotteryRegionEnum::DT))
                )
                ->add(
                    'display',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Display'))
                        ->choices([
                            '' => 'Default',
                            'most' => 'Về nhiều nhất',
                            'least' => 'Về ít nhất',
                            'none' => 'Chưa xuất hiện',
                        ])
                );
        });

        Shortcode::register(
            'lottery-thong-ke-loto-kep',
            __('Lottery Thống kê loto kép'),
            __('Lottery Thống kê loto kép'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $doubleNumbers = ['00', '11', '22', '33', '44', '55', '66', '77', '88', '99'];

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit($range)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->whereIn(DB::raw('RIGHT(number, 2)'), $doubleNumbers)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-loto-kep', compact('city', 'numbers', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-loto-kep', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-dac-biet',
            __('Lottery Thống kê đặc biệt'),
            __('Lottery Thống kê đặc biệt'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $type = $shortcode->type ?: 'default';

                $year = LotteryStatistic::getRequestYear();

                $startDate = Carbon::today();

                switch ($type) {
                    case 'week':
                    case 'month':
                    case 'year':
                        if ($year == date('Y')) {
                            $startDate = Carbon::today();
                        } else {
                            $startDate = Carbon::create($year)->endOfYear();
                        }

                        break;
                    default:
                        $startDate = Carbon::today();

                        break;
                }

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->when($type == 'default', function ($query) {
                        $range = LotteryStatistic::getRequestRange();

                        $query->limit($range);
                    })
                    ->when($type == 'week', function ($query) use ($startDate) {
                        $query->where('date', '>=', $startDate->copy()->startOfYear());
                    })
                    ->when($type == 'month', function ($query) use ($startDate) {
                        $query->whereYear('date', $startDate->copy()->year);
                    })
                    ->when($type == 'year', function ($query) {
                        $year = LotteryStatistic::getRequestYear();
                        if ($year == date('Y')) {
                            $years = range($year - 3, $year);
                        } else {
                            $years = [$year - 1, $year, $year + 1];
                        }
                        $query->whereIn(DB::raw('YEAR(date)'), $years);
                    })
                    ->when($type == 'all', function ($query) {
                        $year = LotteryStatistic::getRequestYear();

                        $query->where(DB::raw('YEAR(date)'), $year);
                    })
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                        DB::raw('WEEKOFYEAR(date) AS week_of_year'),
                        DB::raw('YEARWEEK(date, 3) AS iso_year_week'),
                        DB::raw('DAYOFWEEK(date) - 1 AS day_of_week'),
                    ])
                    ->where('city_id', $city->id)
                    ->where('level', LotteryResultLevelEnum::ZERO)
                    ->latest('date')
                    ->clone()
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->getQuery()
                    ->get();

                $extraNumbers = collect();
                $number = '';
                switch ($type) {
                    case 'default':
                        $number = LotteryStatistic::getLastTwoDigits($numbers->first()->last_two_digits);
                        $extraNumbers = $model
                            ->where(DB::raw('RIGHT(number, 2)'), $number)
                            ->getQuery()
                            ->get();

                        break;
                    case 'week':
                        $number = LotteryStatistic::getRequestWeekOfYear($numbers->first()->week_of_year);
                        $extraNumbers = $model
                            ->addSelect([
                                DB::raw('YEAR(date) AS year'),
                            ])
                            ->where(DB::raw('WEEKOFYEAR(date)'), $number)
                            ->limit(70)
                            ->getQuery()
                            ->get();

                        break;
                    case 'month':
                        $extraNumbers = $model
                            ->addSelect([
                                DB::raw('YEAR(date) AS year'),
                            ])
                            ->where(DB::raw('MONTH(date)'), LotteryStatistic::getRequestMonth())
                            ->limit(200)
                            ->getQuery()
                            ->get();

                        break;
                }

                $compact = compact('startDate', 'endDate', 'city', 'numbers', 'cityDates', 'extraNumbers', 'number');

                return Theme::partial('lottery.shortcodes.statistics.dac-biet.' . $type, $compact);
            }
        );

        Shortcode::setAdminConfig('lottery-thong-ke-dac-biet', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'type',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Type'))
                        ->choices([
                            'default' => 'Mặc định',
                            'week' => 'Theo tuần',
                            'month' => 'Theo tháng',
                            'year' => 'Theo năm',
                            'all' => 'Theo tổng',
                        ])
                );
        });

        Shortcode::register(
            'lottery-thong-ke-2-diem',
            __('Lottery Thống kê 2 điểm'),
            __('Lottery Thống kê 2 điểm'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region ?: LotteryRegionEnum::MB;
                $type = $shortcode->type ?: 'default';

                $dayOfWeek = LotteryStatistic::getRequestDayOfWeek();

                $cities = LotterySupport::getCitiesByRegion($region, $dayOfWeek);

                if ($region == LotteryRegionEnum::MB) {
                    $city = $cities->first();
                } else {
                    $city = LotteryStatistic::getRequestCity(false, $region);

                    if (! $city || $cities->where('id', $city->id)->isEmpty()) {
                        $city = $cities->where('region', $region)->where('parent_id')->first();
                        request()->merge(['city_code' => $city->subcode]);
                    }
                }

                $startDate = Carbon::today();

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit(15)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->oldest('id')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-2-diem', compact('city', 'numbers', 'cityDates', 'shortcode', 'type'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-2-diem', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'description',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Description'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(LotteryRegionEnum::labels())
                )
                ->add(
                    'type',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Type'))
                        ->choices([
                            'default' => '2 điểm duy nhất',
                            'flip' => '2 điểm lật liên tục',
                            'many' => '2 điểm về nhiều',
                        ])
                );
        });

        Shortcode::register(
            'lottery-check',
            __('Lottery check'),
            __('Lottery check'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $date = LotteryStatistic::getRequestDate();

                $number = request()->input('number');

                $cityDate = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', $date)
                    ->getQuery()
                    ->first();

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', $date)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $prizes = LotteryStatistic::findPrize($numbers, $city->region, $number);

                return Theme::partial('lottery.shortcodes.check', compact('city', 'numbers', 'cityDate', 'shortcode', 'date', 'prizes'));
            }
        )
        ->setAdminConfig('lottery-check', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-tong-hop',
            __('Lottery thong ke tong hop'),
            __('Lottery thong ke tong hop'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $date = LotteryStatistic::getRequestDate();

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $endDate = $startDate->copy()->subDays($range);

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('RIGHT(number, 1) AS last_one_digits'),
                        DB::raw('SUBSTRING(number FROM LENGTH(number) - 1 FOR 1) AS first_one_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-tong-hop', compact('city', 'numbers', 'range', 'shortcode', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-tong-hop', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'url_thong_ke_dac_biet',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('URL Thống kê giải Đặc Biệt'))
                )
                ->add(
                    'url_thong_ke_tat_ca_cac_giai',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('URL Thống kê tất cả các giải'))
                )
                ->add(
                    'url_thong_ke_so_lan_chua_ve',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('URL Thống kê số lần chưa về'))
                )
                ->add(
                    'url_thong_ke_so_ve_cung',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('URL Thống kê số về cùng'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-chu-ky',
            __('Lottery thong ke chu ky'),
            __('Lottery thong ke chu ky'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $date = LotteryStatistic::getRequestDate();

                $startDate = Carbon::today();

                $range = LotteryStatistic::getRequestRange();

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit($range)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('RIGHT(number, 1) AS last_one_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-chu-ky', compact('city', 'numbers', 'range', 'shortcode', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-chu-ky', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-tan-suat-loto',
            __('Lottery thong ke tan suat loto'),
            __('Lottery thong ke tan suat loto'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $date = LotteryStatistic::getRequestDate();

                $startDate = $date;

                $range = max(min(request()->integer('range', 100), 1000), 1);

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit($range)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('RIGHT(number, 1) AS last_one_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-tan-suat-loto', compact('city', 'numbers', 'range', 'shortcode', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-tan-suat-loto', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-thong-ke-tan-suat-loto-cap',
            __('Lottery thong ke tan suat loto cap'),
            __('Lottery thong ke tan suat loto cap'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $date = LotteryStatistic::getRequestDate();

                $startDate = $date;

                $range = max(min(request()->integer('range', 100), 1000), 1);

                $cityDates = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', '<=', $startDate)
                    ->limit($range)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($city->region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('RIGHT(number, 1) AS last_one_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.statistics.thong-ke-tan-suat-loto-cap', compact('city', 'numbers', 'range', 'shortcode', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-thong-ke-tan-suat-loto-cap', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                );
        });

        Shortcode::register(
            'lottery-ket-qua-loto',
            __('Lottery ket qua loto'),
            __('Lottery ket qua loto'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;

                $cities = LotterySupport::getCitiesByRegion($region);

                $cityDates = LotteryCityDate::query()
                    ->whereIn('city_id', $cities->pluck('id')->all())
                    ->latest('date')
                    ->getQuery()
                    ->paginate();

                $startDate = $cityDates->first()->date;
                $endDate = $cityDates->last()->date;

                $model = LotteryRegionEnum::getModelQuery($region);

                $numbers = $model
                    ->select(['*'])
                    ->addSelect([
                        DB::raw('RIGHT(number, 2) AS last_two_digits'),
                        DB::raw('DATE_FORMAT(date, \'%d-%m-%Y\') AS date_formatted'),
                    ])
                    ->where('date', '<=', $startDate)
                    ->where('date', '>=', $endDate)
                    // ->where('city_id', $city->id)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                return Theme::partial('lottery.shortcodes.ket-qua-loto', compact('numbers', 'shortcode', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-ket-qua-loto', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->choices(LotteryRegionEnum::labels())
                );
        });

        Shortcode::register(
            'lottery-region-range',
            __('Lottery region range'),
            __('Lottery region range'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;
                $range = $shortcode->range ?: '30-ngay';

                $parentCity = LotterySupport::getParentCity($region);

                $cities = LotterySupport::getCitiesByRegion($region);

                Theme::set('region-submenu', $region);

                $startDate = Carbon::today();
                $endDate = Carbon::yesterday();

                switch ($range) {
                    case '200-ngay':
                    case '100-ngay':
                    case '90-ngay':
                    case '60-ngay':
                    case '30-ngay':
                        $endDate = $startDate->copy()->subDays(Str::replace('-ngay', '', $range));

                        break;
                    default:
                        $startDate = Carbon::yesterday();

                        break;
                }

                $cityDates = LotteryCityDate::query()
                    ->whereIn('city_id', $cities->pluck('id')->all())
                    ->whereDate('date', '<=', $startDate)
                    ->whereDate('date', '>=', $endDate)
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $numbers = LotterySupport::getNumbersByRanges($region, [$startDate, $endDate]);
                $period = collect();

                foreach ($cityDates->groupBy('date')->keys() as $date) {
                    $period[] = Carbon::create($date);
                }

                return Theme::partial('lottery.shortcodes.region-detail', compact('parentCity', 'region', 'period', 'cities', 'numbers', 'cityDates'));
            }
        )
        ->setAdminConfig('lottery-region-range', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->choices(LotteryRegionEnum::labels())
                )
                ->add(
                    'range',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->choices(Arr::mapWithKeys(LotterySupport::getRegionRanges(), fn ($value, $key) => [$key => $value['label']]))
                );
        });

        Shortcode::register(
            'lottery-region-day',
            __('Lottery region day'),
            __('Lottery region day'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;
                $day = $shortcode->day ?: 'thu-2';

                if (in_array($region, ['dientoan123', 'thantai4', 'dientoan6x36'])) {
                    $cities = LotterySupport::getCities()
                        ->where('code', $region);

                    $region = LotteryRegionEnum::DT();
                } else {
                    $cities = LotterySupport::getCitiesByRegion($region);
                }

                $parentCity = LotterySupport::getParentCity($region);

                $dayOfWeek = LotterySupport::dayOfWeekBySlug($day);

                $dates = LotteryCityDate::query()
                    ->select(['date'])
                    ->whereIn('city_id', $cities->pluck('id')->all())
                    ->whereRaw('DAYOFWEEK(date) = ?', [$dayOfWeek['key'] + 1])
                    ->latest('date')
                    ->groupBy('date')
                    ->getQuery()
                    ->simplePaginate(7);

                $cityDates = LotteryCityDate::query()
                    ->whereIn('city_id', $cities->pluck('id')->all())
                    ->whereIn('date', $dates->pluck('date')->all())
                    ->latest('date')
                    ->getQuery()
                    ->get();

                $numbers = LotterySupport::getNumbersByCityDates($region, $cityDates);

                $period = collect();
                foreach ($dates as $date) {
                    $period[] = Carbon::create($date->date);
                }

                $datesPagination = $dates;

                return Theme::partial('lottery.shortcodes.region-detail', compact('parentCity', 'region', 'period', 'cities', 'numbers', 'cityDates', 'datesPagination'));
            }
        )
        ->setAdminConfig('lottery-region-day', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->choices(LotteryRegionEnum::labels() + [
                            'dientoan123' => 'Điện Toán 123',
                            'thantai4' => 'Thần Tài 4',
                            'dientoan6x36' => 'Điện Toán 6x36',
                        ])
                )
                ->add(
                    'day',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->choices(Arr::mapWithKeys(LotterySupport::daysOfWeek(), fn ($value, $key) => [$value['slug'] => $value['name']]))
                );
        });


        Shortcode::register(
            'lottery-finalize-predicted',
            __('Lottery Chốt số lô dự đoán'),
            __('Lottery Chốt số lô dự đoán'),
            function (ShortcodeCompiler $shortcode) {
                $region = $shortcode->region;
                $city = LotteryStatistic::getRequestCity();

                return Theme::partial('lottery.shortcodes.finalize-predicted', compact('region', 'shortcode', 'city'));
            }
        )
        ->setAdminConfig('lottery-finalize-predicted', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->choices(Arr::except(LotteryRegionEnum::labels(), LotteryRegionEnum::DT))
                );
        });

        Shortcode::register(
            'lottery-pascale',
            __('Lottery pascale'),
            __('Lottery pascale'),
            function (ShortcodeCompiler $shortcode) {
                $city = LotteryStatistic::getRequestCity();
                $date = LotteryStatistic::getRequestDate();

                $region = $shortcode->region ?: $city->region;

                $model = LotteryRegionEnum::getModelQuery($region);

                // Lấy dữ liệu theo số ngày được chỉ định
                // $days = (int) ($shortcode->days ?: 1);
                // $startDate = Carbon::parse($date)->subDays($days - 1);

                $cityDate = LotteryCityDate::query()
                    ->where('city_id', $city->id)
                    ->where('date', $date)
                    ->getQuery()
                    ->first();

                if ($cityDate) {
                    $numbers = $model
                        ->select(['*'])
                        ->where('lottery_id', $cityDate->id)
                        ->orderBy('date', 'desc')
                        ->getQuery()
                        ->get();
                } else {
                    $numbers = collect();
                }

                request()
                    ->merge([
                        'day_of_week' => $date->dayOfWeek,
                        'city_code' => $city->subcode,
                        'date' => LotterySupport::formatDate($date),
                    ]);

                // Nhóm theo ngày để hiển thị nhiều tam giác nếu cần
                $numbersByDate = $numbers->groupBy('date');

                return Theme::partial('lottery.shortcodes.pascale', compact('shortcode', 'numbers', 'numbersByDate', 'city', 'date'));
            }
        )
        ->setAdminConfig('lottery-pascale', function (array $attributes) {
            return ShortcodeForm::createFromArray($attributes)
                ->add(
                    'title',
                    TextField::class,
                    TextFieldOption::make()
                        ->label(__('Title'))
                        ->defaultValue('Tam giác Pascale')
                )
                ->add(
                    'region',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('Region'))
                        ->choices(Arr::except(LotteryRegionEnum::labels(), LotteryRegionEnum::DT))
                )->add(
                    'city_id',
                    HtmlField::class,
                    HtmlFieldOption::make()
                        ->content(Theme::partial('lottery.cities-by-region', compact('attributes')))
                )
                ->add(
                    'by',
                    SelectField::class,
                    SelectFieldOption::make()
                        ->label(__('By'))
                        ->choices([
                            '' => 'Default',
                            'yesterday' => 'Yesterday',
                        ])
                );
        });
    }
});
